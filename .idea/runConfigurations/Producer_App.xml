<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Producer App" type="JetRunConfigurationType">
    <option name="ALTERNATIVE_JRE_PATH" value="11" />
    <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
    <envs>
      <env name="AWS_ACCESS_KEY_ID" value="mngr" />
      <env name="AWS_SECRET_ACCESS_KEY" value="mngr" />
      <env name="KINESIS_NAME" value="TEST_STREAM" />
    </envs>
    <option name="MAIN_CLASS_NAME" value="com.loyalty.nova.offer.state.writer.ProducerAppKt" />
    <module name="nova-offer-state-writer.nova-offer-state-writer-producer.main" />
    <shortenClasspath name="NONE" />
    <option name="VM_PARAMETERS" value="-Dspring.profiles.active=local" />
    <method v="2">
      <option name="Make" enabled="true" />
    </method>
  </configuration>
</component>